"use client";

import { But<PERSON> } from "@/components/ui/button";
import { LogOut } from "lucide-react";
import { logout } from "@/actions/auth";
import { useRouter } from "next/navigation";

export function LogoutButton() {
	const router = useRouter();

	const handleLogout = async () => {
		await logout();
		router.push("/login");
	};

	return (
		<Button variant="ghost" onClick={handleLogout} className="w-full justify-start">
			<LogOut className="mr-2 h-4 w-4" />
			Logout
		</Button>
	);
}
