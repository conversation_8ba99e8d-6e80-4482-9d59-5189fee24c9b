"use client";

import type * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { TooltipProvider } from "@/components/ui/tooltip";
// import { AppProgressBar as ProgressBar } from "next-nprogress-bar";
import { NuqsAdapter } from "nuqs/adapters/next/app";

export function Providers({ children, ...props }: React.ComponentProps<typeof NextThemesProvider>) {
	return (
		<NextThemesProvider {...props}>
			<NuqsAdapter>
				<TooltipProvider>{children}</TooltipProvider>
			</NuqsAdapter>
			{/* <ProgressBar
				height="4px"
				color="#3b82f6"
				options={{ showSpinner: true }}
				spinnerPosition="bottom-right"
				disableSameURL={true}
			/> */}
		</NextThemesProvider>
	);
}
