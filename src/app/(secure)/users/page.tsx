import UsersList from "./components/items-list";
import UsersFilter from "./components/users-filter";
import ItemsPagination from "./components/pagination";
import { Button } from "@/components/ui/button";
import { getUserService } from "@/actions/config";
import Link from "next/link";
import { getSession, getAbility } from "@/actions/config";
import { redirect } from "next/navigation";

export default async function Users({
	searchParams,
}: {
	searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
	const awaitedSearchParams = await searchParams;
	const searchParamsString = JSON.stringify(awaitedSearchParams, null, 2);

	const userService = await getUserService();
	const result = await userService.findUsers({
		limit: awaitedSearchParams.limit ? Number(awaitedSearchParams.limit) : 10,
		page: awaitedSearchParams.page ? Number(awaitedSearchParams.page) : 1,
		filter: {
			username: awaitedSearchParams.username as string | undefined,
		},
		// sort: {
		// 	name: "desc",
		// },
	});
	const ability = await getAbility();
	console.log("ability", ability);
	if (!ability.can("manage", "User")) {
		redirect("/no-access");
		// return (
		// 	<div className="flex flex-col items-center justify-center h-full p-8">
		// 		<div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
		// 			<strong className="font-bold">Access Denied!</strong>
		// 			<span className="block sm:inline"> You don't have permission to view this page.</span>
		// 		</div>
		// 	</div>
		// );
	}

	return (
		<div className="flex flex-col gap-4 p-4">
			<h1 className="text-2xl font-bold">Users</h1>
			<div className="flex flex-row justify-end">
				<Button type="button">
					<Link href="/users/new" className="w-full">
						Add new user
					</Link>
				</Button>
			</div>
			<UsersFilter />

			<UsersList items={result.items} />

			<ItemsPagination {...result.pagination} />
		</div>
	);
}
