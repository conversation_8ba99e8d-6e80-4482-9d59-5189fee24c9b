import UserForm from "../components/user-form";
import { getUserService } from "@/actions/config";

export default async function UserPage({
	params,
}: {
	params: Promise<{ id: string }>;
}) {
	const userId = (await params).id;
	const userService = await getUserService();
	const user = await userService.getUserById(userId);
	if (!user) {
		return <div>User not found</div>;
	}
	return (
		<div className="flex flex-col gap-4">
			<div className="text-2xl font-bold">User: {userId}</div>
			<div>
				<UserForm
					initialData={{
						id: user.id,
						username: user.username || "",
						email: user.email || "",
						name: user.name || "",
						roleId: user.roleId || 0,
					}}
				/>
			</div>
		</div>
	);
}
