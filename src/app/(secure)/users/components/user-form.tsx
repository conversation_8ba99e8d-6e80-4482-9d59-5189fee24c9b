"use client";

import { z } from "zod/v4";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { createUser, updateUser } from "@/actions/users";
import { useTransition } from "react";
import { Loader2 } from "lucide-react";

// Form validation schema based on database schema
const formSchema = z.object({
	id: z.uuid().optional(),
	name: z.string().min(1, "Name is required").optional(),
	username: z.string().min(1, "Username is required"),
	email: z.email("Invalid email address"),
	password: z.string().min(6, "Password must be at least 6 characters").optional(),
	roleId: z.number().int().default(0).optional(),
});

type FormObject = z.infer<typeof formSchema>;

export default function UserForm({ initialData }: { initialData?: FormObject }) {
	const router = useRouter();
	const [isPending, startTransition] = useTransition();

	const form = useForm<FormObject>({
		resolver: zodResolver(formSchema),
		defaultValues: initialData || {
			id: undefined,
			name: "",
			username: "",
			email: "",
			password: "",
			roleId: 0,
		},
	});

	async function onSubmit(values: z.infer<typeof formSchema>) {
		// Do something with the form values.
		// ✅ This will be type-safe and validated.
		// console.log(values)
		// toast(JSON.stringify(values, null, 2));
		startTransition(async () => {
			try {
				if (values.id) {
					// update user
					// toast("Updating user NOT IMPLEMENTED yet");
					await updateUser({
						...values,
						id: values.id,
					});
					toast("User updated");
					return;
				}
				const result = await createUser({
					name: values.name,
					username: values.username,
					email: values.email,
					password: values.password || "12345",
					roleId: values.roleId || 0,
				});
				toast.success("User created");
			} catch (err) {
				if (err instanceof Error) {
					toast.error(err.message);
					return;
				}
				toast.error("An error occurred while creating user");
			}
		});
	}

	return (
		<div>
			<Card>
				<CardHeader>
					<CardTitle>User Form</CardTitle>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)}>
							<div className="space-y-4">
								<FormField
									control={form.control}
									name="username"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Username</FormLabel>
											<FormControl>
												<Input placeholder="Enter username" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="email"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Email</FormLabel>
											<FormControl>
												<Input type="email" placeholder="<EMAIL>" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Full Name</FormLabel>
											<FormControl>
												<Input placeholder="Enter full name" {...field} value={field.value ?? ""} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								{!initialData?.id && (
									<FormField
										control={form.control}
										name="password"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Password</FormLabel>
												<FormControl>
													<Input type="password" placeholder="Enter password" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}
								<FormField
									control={form.control}
									name="roleId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Role</FormLabel>
											<Select onValueChange={(value) => field.onChange(Number(value))} value={field.value?.toString()}>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select a role" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="0">Public</SelectItem>
													<SelectItem value="1">Admin</SelectItem>
													<SelectItem value="3">Staff</SelectItem>
													<SelectItem value="2">User</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
							<div className="w-full flex flex-row justify-end gap-2">
								<Button type="button" className="w-30" variant="outline" onClick={() => router.back()}>
									{initialData?.id ? "Back" : "Cancel"}
								</Button>
								<Button type="submit" disabled={isPending} className="w-30">
									{isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : "Save"}
								</Button>
							</div>
						</form>
					</Form>
				</CardContent>
				{/* <CardFooter></CardFooter> */}
			</Card>
		</div>
	);
}
