"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState, useTransition, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Loader2 } from "lucide-react";

export default function UsersFilter() {
	const router = useRouter();
	const searchParams = useSearchParams();

	const [isPending, startTransition] = useTransition();

	const [username, setUsername] = useState("");

	// Update state when URL changes (including back button)
	useEffect(() => {
		setUsername(searchParams.get("username") || "");
	}, [searchParams]);

	const apply = () => {
		startTransition(() => {
			const params = new URLSearchParams();
			if (username) params.set("username", username);
			if (searchParams.get("limit")) {
				params.set("limit", searchParams.get("limit") || "10");
			}

			router.push(`?${params.toString()}`);
		});
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		apply();
	};

	return (
		<div>
			<Card className="shadow-none">
				<CardHeader>
					<CardTitle>Filters</CardTitle>
				</CardHeader>
				<CardContent>
					<form onSubmit={handleSubmit}>
						<div className="space-y-2">
							<Label htmlFor="username">Username</Label>
							<Input id="username" placeholder="" value={username} onChange={(e) => setUsername(e.target.value)} />
						</div>
						<div className="flex flex-row justify-end">
							<Button type="submit" variant="outline" className="mt-4 w-30">
								{isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : "Apply"}
							</Button>
						</div>
					</form>
				</CardContent>
			</Card>
		</div>
	);
}
