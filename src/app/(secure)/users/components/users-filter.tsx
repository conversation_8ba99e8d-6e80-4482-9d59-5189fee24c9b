"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState, useTransition, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Loader2 } from "lucide-react";

export default function UsersFilter() {
	const router = useRouter();
	const searchParams = useSearchParams();

	const [isPending, startTransition] = useTransition();

	const [username, setUsername] = useState("");
	const [isActive, setIsActive] = useState<string>("");

	// Update state when URL changes (including back button)
	useEffect(() => {
		setUsername(searchParams.get("username") || "");
		setIsActive(searchParams.get("isActive") || "");
	}, [searchParams]);

	const apply = () => {
		startTransition(() => {
			const params = new URLSearchParams();
			if (username) params.set("username", username);
			if (isActive) params.set("isActive", isActive);
			if (searchParams.get("limit")) {
				params.set("limit", searchParams.get("limit") || "10");
			}

			router.push(`?${params.toString()}`);
		});
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		apply();
	};

	return (
		<div>
			<Card className="shadow-none">
				<CardHeader>
					<CardTitle>Filters</CardTitle>
				</CardHeader>
				<CardContent>
					<form onSubmit={handleSubmit}>
						<div className="space-y-4">
							<div className="space-y-2">
								<Label htmlFor="username">Username</Label>
								<Input id="username" placeholder="" value={username} onChange={(e) => setUsername(e.target.value)} />
							</div>
							<div className="space-y-2">
								<Label htmlFor="status">Status</Label>
								<Select value={isActive} onValueChange={setIsActive}>
									<SelectTrigger>
										<SelectValue placeholder="All users" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="">All users</SelectItem>
										<SelectItem value="true">Active only</SelectItem>
										<SelectItem value="false">Inactive only</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>
						<div className="flex flex-row justify-end">
							<Button type="submit" variant="outline" className="mt-4 w-30">
								{isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : "Apply"}
							</Button>
						</div>
					</form>
				</CardContent>
			</Card>
		</div>
	);
}
