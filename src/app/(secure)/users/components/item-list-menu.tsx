import { Button } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuShortcut,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { EllipsisVertical } from "lucide-react";
import Link from "next/link";
// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export default function DropdownMenuWithShortcuts(options: { item: any }) {
	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="link" size="icon">
					<EllipsisVertical />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent className="w-60">
				<DropdownMenuItem>
					<Link href={`/users/${options.item.id}`} className="w-full">
						View
					</Link>
				</DropdownMenuItem>
				<DropdownMenuItem>
					New Window <DropdownMenuShortcut>⌘N</DropdownMenuShortcut>
				</DropdownMenuItem>
				<DropdownMenuItem>
					New Incognito Window <DropdownMenuShortcut>⌘⇧N</DropdownMenuShortcut>
				</DropdownMenuItem>
				<DropdownMenuSeparator />
				<DropdownMenuSub>
					<DropdownMenuSubTrigger>History</DropdownMenuSubTrigger>
					<DropdownMenuSubContent className="w-40">
						<DropdownMenuItem>
							History <DropdownMenuShortcut>⌘Y</DropdownMenuShortcut>
						</DropdownMenuItem>
						<DropdownMenuItem>Grouped History</DropdownMenuItem>
					</DropdownMenuSubContent>
				</DropdownMenuSub>
				<DropdownMenuItem>
					Downloads <DropdownMenuShortcut>⌥⇧L</DropdownMenuShortcut>
				</DropdownMenuItem>
				<DropdownMenuItem>
					Delete browsing data <DropdownMenuShortcut>⇧⌘⌫</DropdownMenuShortcut>
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
