"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import DropdownMenu from "./item-list-menu";

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export default function ItemsList({ items }: { items: any[] }) {
	const [selectedItems, setSelectedItems] = useState<number[]>([]);

	const toggleItem = (itemId: number) => {
		setSelectedItems((prev) => (prev.includes(itemId) ? prev.filter((id) => id !== itemId) : [...prev, itemId]));
	};

	const toggleAll = () => {
		if (selectedItems.length === items.length) {
			setSelectedItems([]);
		} else {
			setSelectedItems(items.map((p) => p.id));
		}
	};

	const isAllSelected = selectedItems.length === items.length && items.length > 0;
	const isSomeSelected = selectedItems.length > 0 && selectedItems.length < items.length;

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		setSelectedItems([]);
	}, [items]);

	return (
		// <div className="grid w-full [&>div]:h-[300px] [&>div]:border [&>div]:rounded-md">
		<div className="grid w-full [&>div]:border [&>div]:rounded-md">
			<Table>
				<TableHeader>
					<TableRow className="z-10 hover:bg-muted [&>*]:whitespace-nowrap sticky top-0 bg-background after:content-[''] after:inset-x-0 after:h-px after:bg-border after:absolute after:bottom-0">
						<TableHead className="w-[50px]">
							<Checkbox
								checked={isAllSelected}
								onCheckedChange={toggleAll}
								{...(isSomeSelected ? { "data-state": "indeterminate" } : {})}
							/>
						</TableHead>
						<TableHead className="pl-4">ID</TableHead>
						<TableHead>Username</TableHead>
						<TableHead>Email</TableHead>
						<TableHead>Name</TableHead>
						<TableHead>Created at</TableHead>
						<TableHead>Updated at</TableHead>
						<TableHead>Role</TableHead>
						<TableHead>Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody className="overflow-hidden">
					{items.map((item) => (
						<TableRow key={item.id} className="odd:bg-muted/50 [&>*]:whitespace-nowrap">
							<TableCell>
								<Checkbox checked={selectedItems.includes(item.id)} onCheckedChange={() => toggleItem(item.id)} />
							</TableCell>
							<TableCell className="pl-4">{item.id}</TableCell>
							<TableCell className="font-medium">{item.username}</TableCell>
							<TableCell>{item.email}</TableCell>
							<TableCell>{item.name}</TableCell>
							<TableCell>{item.createdAt.toLocaleString()}</TableCell>
							<TableCell>{item.updatedAt.toLocaleString()}</TableCell>
							<TableCell>{item.role.name}</TableCell>
							<TableCell>
								<DropdownMenu item={item} />
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
			<Button
				type="button"
				variant="outline"
				className="w-full mt-4"
				onClick={() => {
					toast(`Selected items: ${selectedItems.join(", ")}`);
				}}
			>
				Show selected items
			</Button>
		</div>
	);
}
