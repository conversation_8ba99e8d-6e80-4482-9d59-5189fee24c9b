"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export default function UsersList({ items }: { items: any[] }) {
	const [selectedProducts, setSelectedProducts] = useState<number[]>([]);

	const toggleProduct = (productId: number) => {
		setSelectedProducts((prev) =>
			prev.includes(productId) ? prev.filter((id) => id !== productId) : [...prev, productId],
		);
	};

	const toggleAll = () => {
		if (selectedProducts.length === items.length) {
			setSelectedProducts([]);
		} else {
			setSelectedProducts(items.map((p) => p.id));
		}
	};

	const isAllSelected = selectedProducts.length === items.length && items.length > 0;
	const isSomeSelected = selectedProducts.length > 0 && selectedProducts.length < items.length;

	return (
		<div className="grid w-full [&>div]:h-[300px] [&>div]:border [&>div]:rounded-md">
			<Table>
				<TableHeader>
					<TableRow className="z-10 hover:bg-muted [&>*]:whitespace-nowrap sticky top-0 bg-background after:content-[''] after:inset-x-0 after:h-px after:bg-border after:absolute after:bottom-0">
						<TableHead className="w-[50px]">
							<Checkbox
								checked={isAllSelected}
								onCheckedChange={toggleAll}
								{...(isSomeSelected ? { "data-state": "indeterminate" } : {})}
							/>
						</TableHead>
						<TableHead className="pl-4">ID</TableHead>
						<TableHead>Username</TableHead>
						<TableHead>Email</TableHead>
						<TableHead>Name</TableHead>
						<TableHead>Created at</TableHead>
						<TableHead>Updated at</TableHead>
						<TableHead>Supplier</TableHead>
						<TableHead>Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody className="overflow-hidden">
					{items.map((item) => (
						<TableRow key={item.id} className="odd:bg-muted/50 [&>*]:whitespace-nowrap">
							<TableCell>
								<Checkbox checked={selectedProducts.includes(item.id)} onCheckedChange={() => toggleProduct(item.id)} />
							</TableCell>
							<TableCell className="pl-4">{item.id}</TableCell>
							<TableCell className="font-medium">{item.username}</TableCell>
							<TableCell>{item.email}</TableCell>
							<TableCell>{item.name}</TableCell>
							<TableCell>{item.createdAt.toLocaleString()}</TableCell>
							<TableCell>{item.updatedAt.toLocaleString()}</TableCell>
							<TableCell>{item.supplier}</TableCell>
							<TableCell>{item.dateAdded}</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
			<Button type="button" variant="outline" className="w-full" onClick={() => console.log(selectedProducts)}>
				Show selected items
			</Button>
		</div>
	);
}
