"use client";

import { useRouter, useSearchParams } from "next/navigation";
import {
	Pagination,
	PaginationContent,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function ItemsPagination(params: {
	totalFound: number;
	page: number;
	limit: number;
	totalPages: number;
}) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const currentPage = params.page;
	const totalPages = params.totalPages;
	const pageNumber = params.page;

	const createPageUrl = (pageNumber: number) => {
		const newParams = new URLSearchParams(searchParams.toString());
		newParams.set("page", pageNumber.toString());
		return `/users?${newParams.toString()}`;
	};

	return (
		<div className="flex flex-row justify-between">
			<div className="w-full flex items-center gap-4">
				<Select
					onValueChange={(value) => {
						const newParams = new URLSearchParams(searchParams.toString());
						newParams.set("limit", value);
						newParams.delete("page");
						const newUrl = `/users?${newParams.toString()}`;
						router.push(newUrl);
					}}
					value={searchParams.get("limit") || "10"}
				>
					<SelectTrigger className="w-[80px]">
						<SelectValue placeholder="Pages" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="1">1</SelectItem>
						<SelectItem value="10">10</SelectItem>
						<SelectItem value="20">20</SelectItem>
					</SelectContent>
				</Select>
				<div>
					Showing {params.page} to {params.limit} of {params.totalFound} entries
				</div>
			</div>
			<Pagination className="justify-end">
				<PaginationContent>
					<PaginationItem>
						<PaginationPrevious href={createPageUrl(currentPage > 1 ? currentPage - 1 : 1)} />
					</PaginationItem>

					{[...Array(totalPages)].map((_, i) => (
						// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
						<PaginationItem key={i + 1}>
							<PaginationLink href={createPageUrl(i + 1)} isActive={currentPage === i + 1}>
								{i + 1}
							</PaginationLink>
						</PaginationItem>
					))}

					<PaginationItem>
						<PaginationNext href={createPageUrl(currentPage < totalPages ? currentPage + 1 : totalPages)} />
					</PaginationItem>
				</PaginationContent>
			</Pagination>
		</div>
	);
}
