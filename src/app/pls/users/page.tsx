// import { config } from "@/app-config";
import { getSession, getAbility } from "@/actions/config";
import { redirect } from "next/navigation";

export default async function Users() {
	const session = await getSession();
	if (!session) {
		redirect("/login?redirect=/pls/users");
	}
	const ability = await getAbility();

	// Instead of redirecting, render an error message
	if (!ability.can("read", "User")) {
		return (
			<div className="flex flex-col items-center justify-center h-full p-8">
				<div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
					<strong className="font-bold">Access Denied!</strong>
					<span className="block sm:inline"> You don't have permission to view this page.</span>
				</div>
			</div>
		);
	}

	return <div>Users</div>;
}
