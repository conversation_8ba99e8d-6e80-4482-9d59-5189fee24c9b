import {
	Sidebar,
	<PERSON>bar<PERSON>ontent,
	Sidebar<PERSON>ooter,
	SidebarGroup,
	SidebarGroupContent,
	SidebarGroupLabel,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
} from "@/components/ui/sidebar";
import { LogoutButton } from "@/components/logout-btn";
import { Calendar, Home, Inbox, Search, Settings } from "lucide-react";
import Link from "next/link";
import { ModeToggle } from "@/components/mode-toggle";

// Menu items.
const items = [
	{
		title: "Home",
		url: "#",
		icon: Home,
		mode: "href",
	},
	{
		title: "Dashboard",
		url: "/dashboard",
		icon: Inbox,
		mode: "href",
	},
	{
		title: "PLS",
		url: "/pls",
		icon: Calendar,
		mode: "link",
	},
	{
		title: "PLS - Users",
		url: "/pls/users",
		icon: Search,
		mode: "link",
	},
	{
		title: "Settings",
		url: "#",
		icon: Settings,
		mode: "link",
	},
];

export function AppSidebar() {
	return (
		<Sidebar>
			<SidebarHeader />
			<SidebarContent>
				<SidebarGroup>
					<SidebarGroupLabel>Application</SidebarGroupLabel>
					<SidebarGroupContent>
						<SidebarMenu>
							{items.map((item) => (
								<SidebarMenuItem key={item.title}>
									<SidebarMenuButton asChild>
										{item.mode === "link" ? (
											<Link href={item.url}>
												<item.icon />
												<span>{item.title}</span>
											</Link>
										) : (
											<a href={item.url}>
												<item.icon />
												<span>{item.title}</span>
											</a>
										)}
									</SidebarMenuButton>
								</SidebarMenuItem>
							))}
						</SidebarMenu>
					</SidebarGroupContent>
				</SidebarGroup>
				<SidebarGroup />
				<SidebarGroup />
				<p>test</p>
			</SidebarContent>
			<SidebarFooter>
				<ModeToggle />
				<LogoutButton />
			</SidebarFooter>
		</Sidebar>
	);
}
