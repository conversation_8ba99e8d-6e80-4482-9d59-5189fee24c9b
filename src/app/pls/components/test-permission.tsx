"use client";

import type { Context } from "@/features/context";
import { toast } from "sonner";

interface TestPermissionProps {
	ctx: Context;
	title?: string; // New optional prop
}

export default function TestPermission({ ctx, title = "Test Permission" }: TestPermissionProps) {
	if (!ctx) {
		return <div>No session</div>;
	}

	if (!ctx.ability.can("read", "Dashboard")) {
		toast("You can not read the dashboard");
	}

	return (
		<div>
			<p>{title}</p>
			<p>Role: {ctx.user.roleId}</p>
			<p>Can read Invoice: {`${ctx.ability.can("read", "Invoice")}`}</p>
			<p>Can read Entity: {`${ctx.ability.can("read", "Entity")}`}</p>
			<p>Can read Dashboard: {`${ctx.ability.can("read", "Dashboard")}`}</p>
		</div>
	);
}
