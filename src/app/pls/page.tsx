"use server";

import { redirect } from "next/navigation";
import { getSession, getUserService } from "@/actions/config";

export default async function PLS() {
	let errorMessage = "";
	const session = await getSession();
	if (!session) {
		redirect("/login?redirect=/pls");
	}
	const userService = await getUserService();
	try {
		const user = await userService.getUserById(session.user.id);
		console.log("USER:", user);
	} catch (err) {
		console.log("ERR:", err);
		if (err instanceof Error) {
			errorMessage = err.message;
		}
	}
	return (
		<div className="flex flex-col h-full">
			{!session && <p className="text-red-500">No Session</p>}
			{errorMessage && errorMessage.length > 0 && <p className="text-red-500">{errorMessage}</p>}
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			{/* <p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Explicabo quo inventore pariatur autem iusto laudantium
				saepe a, aspernatur exercitationem ratione ullam officia. Repudiandae sapiente sit porro, ab officiis aliquam
				fuga.
			</p> */}
		</div>
	);
}
