import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "./components/app-sidebar";
import { CustomTrigger } from "./components/toggle-sidebar";

export default function PLSLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<SidebarProvider>
			<AppSidebar />
			<main className="w-full">
				{/* <SidebarTrigger /> */}
				<header className="sticky top-0 z-50 bg-gradient-to-r from-sky-500 to-indigo-600 text-white shadow-lg">
					{/* <div className="container mx-auto px-4 sm:px-6 lg:px-8"> */}
					<div className="flex items-center justify-between px-4 h-16">
						<div className="flex flex-row gap-4">
							<CustomTrigger />
							<h1 className="text-2xl font-bold">PLS</h1>
						</div>
					</div>
					{/* </div> */}
				</header>
				<div className="flex flex-col p-4">{children}</div>
			</main>
		</SidebarProvider>
	);
}
