import { pgTable, text, timestamp, uuid, integer, type AnyPgColumn, type PgUUID, boolean } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { rolesTable } from "./roles";
import { v7 as uuidv7 } from "uuid";

export const usersTable = pgTable("users", {
	id: uuid("id")
		.primaryKey()
		.$default(() => uuidv7()), // Unique ID
	name: text("name"),
	username: text("username").notNull().unique(),
	email: text("email").notNull().unique(),
	password: text("password").notNull(),
	// isActive: boolean("is_active").notNull().default(true),
	roleId: integer("role_id")
		.default(0)
		.references(() => rolesTable.id, {
			onUpdate: "cascade",
		}),
	createdAt: timestamp("created_at", { withTimezone: true }).notNull().defaultNow(),
	createdBy: uuid("created_by").references((): AnyPgColumn => usersTable.id, {
		onDelete: "set null",
		onUpdate: "cascade",
	}),
	updatedAt: timestamp("updated_at", { withTimezone: true })
		.notNull()
		.defaultNow()
		.$onUpdate(() => new Date()),
	updatedBy: uuid("updated_by").references((): AnyPgColumn => usersTable.id, {
		onDelete: "set null",
		onUpdate: "cascade",
	}),
});

export const usersRelations = relations(usersTable, ({ one }) => ({
	role: one(rolesTable, {
		fields: [usersTable.roleId],
		references: [rolesTable.id],
	}),
	createdBy: one(usersTable, {
		fields: [usersTable.createdBy],
		references: [usersTable.id],
	}),
	updatedBy: one(usersTable, {
		fields: [usersTable.updatedBy],
		references: [usersTable.id],
	}),
}));
