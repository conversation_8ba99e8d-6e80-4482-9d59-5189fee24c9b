"use server";

import { getUserService } from "./config";
import type { InsertUserData, UpdateUserData } from "@/features/users";

export async function createUser(user: InsertUserData) {
	const userService = await getUserService();
	const result = await userService.createUser(user);
	return result;
}

export async function updateUser(user: UpdateUserData) {
	const userService = await getUserService();
	const result = await userService.updateUser(user);
	return result;
}

export async function toggleUserActiveStatus(userId: string) {
	const userService = await getUserService();
	await userService.toggleUserActiveStatus(userId);
}
