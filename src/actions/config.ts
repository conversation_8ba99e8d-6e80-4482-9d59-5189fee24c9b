"use server";

import { cache } from "react";
import { CaslAcl } from "@/features/acl/acl-casl";
import type { Context } from "@/features/context";
import { DrizzleUserService } from "@/features/users/drizzle-user-service";
import { JwtSessionService } from "@/features/session/jwt-session-service";
import { DrizzleAuthService } from "@/features/auth/drizzle-auth-service";
import type { IUserService } from "@/features/users";
import type { IAuthService } from "@/features/auth";

export const getSessionService = cache(async () => {
	return new JwtSessionService();
});

export const getSession = cache(async () => {
	console.log("GETTING SESSION");
	const sessionService = await getSessionService();
	const session = await sessionService.getSession();
	return session;
});

const getServiceContext = cache(async () => {
	console.log("GETTING SERVICE CONTEXT");
	const session = await getSession();
	if (!session) return null;

	const aclService = new CaslAcl(session.user.roleId || 0);
	const serviceContext: Context = {
		user: {
			id: session.user.id,
			username: session.user.username,
			email: session.user.email,
			name: session.user.name,
			roleId: session.user.roleId,
			image: session.user.image,
		},
		isAuthenticated: !!session,
		ipAddress: "",
		ability: aclService,
	};
	return serviceContext;
});

export const getAbility = cache(async () => {
	const serviceContext = await getServiceContext();
	if (!serviceContext) {
		return new CaslAcl(0);
	}
	return serviceContext?.ability;
});

export const getUserService = cache(async (): Promise<IUserService> => {
	const serviceContext = await getServiceContext();
	return new DrizzleUserService(serviceContext);
});

export const getAuthService = cache(async (): Promise<IAuthService> => {
	return new DrizzleAuthService();
});
