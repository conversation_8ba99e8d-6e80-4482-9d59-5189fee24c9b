"use server";

import { getAuthService, getSessionService } from "./config";

export async function login(username: string, password: string) {
	const authService = await getAuthService();
	const result = await authService.signIn({
		provider: "credentials",
		credentials: {
			username,
			password,
		},
		redirectUrl: "/",
	});
	// store session
	if (!result.error && result.user) {
		const sessionService = await getSessionService();
		await sessionService.createSession({
			user: {
				id: result.user.id || "",
				email: result.user.email || "",
				name: result.user.name || "",
				username: result.user.username || "",
				roleId: result.user.roleId || 0,
			},
		});
	}
	return result;
}
export async function logout() {
	const authService = await getAuthService();
	const sessionService = await getSessionService();
	await authService.signOut();
	await sessionService.deleteSession();
}
