import type { Acl, Actions, Subjects } from "./acl-interfaces";
import { AbilityBuilder, createMongoAbility, type MongoAbility } from "@casl/ability";

type AppAbility = MongoAbility<[Actions, Subjects]>;

function defineAbilityFor(roleId: number) {
	console.log("DEFINE ABILITY FOR", roleId);
	const { can, cannot, build } = new AbilityBuilder<AppAbility>(createMongoAbility);

	// admin can manage everything
	if (roleId === 1) {
		can("manage", "all");
	}

	if (roleId === 2) {
		// can("read", "all");
		can("read", "Invoice");
		can("read", "User");
	}

	if (roleId === 3) {
		can("read", "Entity");
		can("read", "Invoice");
		can("create", "Invoice");
		can("update", "Invoice");
		can("delete", "Invoice");
	}

	return build();
}

export class CaslAcl implements Acl<Actions, Subjects> {
	private ability;

	constructor(private roleId: number) {
		this.ability = defineAbilityFor(roleId);
	}

	can(action: Actions, subject: Subjects): boolean {
		return this.ability.can(action, subject);
	}
}
