import type {
	FindUsersOptions,
	FindUsersResult,
	InsertUserData,
	IUserRepository,
	IUserService,
	UpdateUserData,
	User,
} from "./user-interfaces";
import { db } from "@/common/db/drizzle";
import { usersTable } from "@/common/db/drizzle/schema";
import type { Context } from "../context";
import { DrizzleUserRepository } from "./drizzle-user-repository";
import { BcryptHasher } from "../auth";

export class DrizzleUserService implements IUserService {
	constructor(
		private ctx?: Context | null,
		private userRepository: IUserRepository = new DrizzleUserRepository(),
	) {}

	async getUserById(id: string): Promise<User | null> {
		// if (!this.ctx) {
		// 	throw new Error("Not authenticated");
		// }
		// if (!this.ctx.ability.can("read", "User")) {
		// 	throw new Error("Unauthorized");
		// }
		return this.userRepository.getUserById(id);
	}

	async getUserByUsername(username: string): Promise<User | null> {
		return null;
	}

	async getUserByEmail(email: string): Promise<User | null> {
		return null;
	}

	async createUser(user: InsertUserData): Promise<string> {
		if (!this.ctx) {
			throw new Error("Not authenticated");
		}
		if (!this.ctx.ability.can("create", "User")) {
			throw new Error("Unauthorized");
		}
		user.password = await new BcryptHasher().hash(user.password);

		const id = await this.userRepository.createUser(user);
		return id;
	}

	async updateUser(user: UpdateUserData): Promise<void> {
		if (!this.ctx) {
			throw new Error("Not authenticated");
		}
		if (!this.ctx.ability.can("update", "User")) {
			throw new Error("Unauthorized");
		}
		await this.userRepository.updateUser(user);
	}

	async findUsers(options: FindUsersOptions): Promise<FindUsersResult> {
		return this.userRepository.findUsers(options);
	}
}
