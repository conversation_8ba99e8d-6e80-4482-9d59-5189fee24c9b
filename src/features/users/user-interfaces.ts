export interface User {
	id?: string;
	username?: string;
	name?: string;
	email?: string;
	roleId?: number;
	password?: string;
	createdAt?: Date;
	createdBy?: string | null;
	updatedAt?: Date;
	updatedBy?: string | null;
	role?: {
		id: number;
		name: string;
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		permissions?: any | null;
	} | null;
}

export type InsertUserData = Omit<User, "id" | "createdAt" | "updatedAt" | "updatedBy"> & {
	password: string;
	username: string;
	roleId: number;
};

export type UpdateUserData = Partial<User> & { id: string };

export type FindUsersOptions = {
	page?: number;
	limit?: number;
	filter?: {
		username?: string;
		email?: string;
		name?: string;
		createdAt?: {
			start?: Date;
			end?: Date;
		};
	};
	sort?: {
		createdAt?: "asc" | "desc";
		updatedAt?: "asc" | "desc";
		name?: "asc" | "desc";
	};
};

export type FindUsersResult = {
	items: User[];
	pagination: {
		totalFound: number;
		page: number;
		limit: number;
		totalPages: number;
	};
};

export interface IUserService {
	getUserById(id: string): Promise<User | null>;
	createUser(user: InsertUserData): Promise<string>;
	updateUser(user: UpdateUserData): Promise<void>;
	getUserByUsername(username: string): Promise<User | null>;
	// getUserByEmail(email: string): Promise<User | null>;
	// createUser(user: User & { password: string }): Promise<User>;
	// getRole(userId: string): Promise<number>;
	findUsers(options: FindUsersOptions): Promise<FindUsersResult>;
}

export interface IUserRepository {
	createUser(user: InsertUserData): Promise<string>;
	getUserById(id: string): Promise<User | null>;
	getUserByUsername(username: string): Promise<User | null>;
	getUserByEmail(email: string): Promise<User | null>;
	findUsers(options: FindUsersOptions): Promise<FindUsersResult>;
	updateUser(user: UpdateUserData): Promise<void>;
}
