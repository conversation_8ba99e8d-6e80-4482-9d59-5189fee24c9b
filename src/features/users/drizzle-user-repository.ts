import type {
	FindUsersOptions,
	FindUsersResult,
	InsertUserData,
	IUserRepository,
	UpdateUserData,
	User,
} from "./user-interfaces";
import { db } from "@/common/db/drizzle";
import { usersTable } from "@/common/db/drizzle/schema";
import { sql, gt, lt, type SQL, ilike, and, asc, desc, eq, Update } from "drizzle-orm";

export class DrizzleUserRepository implements IUserRepository {
	async createUser(user: InsertUserData): Promise<string> {
		const result = await db
			.insert(usersTable)
			.values({
				name: user.name,
				username: user.username,
				email: user.email ?? "",
				password: user.password,
				roleId: user.roleId,
				isActive: user.isActive ?? true,
				createdAt: new Date(),
				createdBy: user.createdBy,
			})
			.returning({ id: usersTable.id });
		return result[0].id;
	}

	async updateUser(user: UpdateUserData): Promise<void> {
		await db.update(usersTable).set(user).where(eq(usersTable.id, user.id));
	}

	async toggleUserActiveStatus(userId: string): Promise<void> {
		// First get the current status
		const user = await db.query.usersTable.findFirst({
			where: (usersTable, { eq }) => eq(usersTable.id, userId),
			columns: { isActive: true },
		});

		if (user) {
			// Toggle the status
			await db
				.update(usersTable)
				.set({ isActive: !user.isActive, updatedAt: new Date() })
				.where(eq(usersTable.id, userId));
		}
	}

	async getUserById(id: string): Promise<User | null> {
		const user = await db.query.usersTable.findFirst({
			where: (usersTable, { eq }) => eq(usersTable.id, id),
		});
		return {
			id: user?.id || "",
			username: user?.username || "",
			name: user?.name || "",
			email: user?.email || "",
			roleId: user?.roleId || 0,
			isActive: user?.isActive ?? true,
			createdAt: user?.createdAt || new Date(),
			createdBy: user?.createdBy || null,
			updatedAt: user?.updatedAt || new Date(),
			updatedBy: user?.updatedBy || null,
		};
	}

	async getUserByUsername(username: string): Promise<User | null> {
		const user = await db.query.usersTable.findFirst({
			where: (usersTable, { eq }) => eq(usersTable.username, username),
		});
		return {
			id: user?.id || "",
			username: user?.username || "",
			name: user?.name || "",
			email: user?.email || "",
			roleId: user?.roleId || 0,
			isActive: user?.isActive ?? true,
			createdAt: user?.createdAt || new Date(),
			createdBy: user?.createdBy || null,
			updatedAt: user?.updatedAt || new Date(),
			updatedBy: user?.updatedBy || null,
		};
	}

	async getUserByEmail(email: string): Promise<User | null> {
		return null;
	}

	async findUsers(options: FindUsersOptions): Promise<FindUsersResult> {
		const { page = 1, limit = 10, filter = {}, sort = {} } = options;
		const offset = (page - 1) * limit;

		// Build query with filters
		const filters: SQL[] = [];
		if (filter.username) filters.push(ilike(usersTable.username, `%${filter.username}%`));
		if (filter.email) filters.push(ilike(usersTable.email, filter.email));
		if (filter.name) filters.push(ilike(usersTable.name, filter.name));
		if (filter.isActive !== undefined) filters.push(eq(usersTable.isActive, filter.isActive));
		if (filter.createdAt?.start) filters.push(gt(usersTable.createdAt, filter.createdAt.start));
		if (filter.createdAt?.end) filters.push(lt(usersTable.createdAt, filter.createdAt.end));

		// Build query with sort order
		const orderClauses: SQL[] = [];
		if (sort.createdAt === "asc") orderClauses.push(asc(usersTable.createdAt));
		if (sort.createdAt === "desc") orderClauses.push(desc(usersTable.createdAt));
		if (sort.updatedAt === "asc") orderClauses.push(asc(usersTable.updatedAt));
		if (sort.updatedAt === "desc") orderClauses.push(desc(usersTable.updatedAt));
		if (sort.name === "asc") orderClauses.push(asc(usersTable.name));
		if (sort.name === "desc") orderClauses.push(desc(usersTable.name));

		const query = db.query.usersTable.findMany({
			where: filters.length ? and(...filters) : undefined,
			orderBy: orderClauses,
			limit,
			offset,
			with: {
				role: true,
			},
		});

		// Count total records for pagination
		const countQuery = db
			.select({ count: sql`count(*)` })
			.from(usersTable)
			.where(and(...filters));

		const totalFound = await countQuery.then((result) => Number(result[0].count));
		const totalPages = Math.ceil(totalFound / limit);
		const items = await query;

		return {
			items: items.map((item) => ({
				id: item.id,
				username: item.username,
				name: item.name || "",
				email: item.email,
				roleId: item.roleId || 0,
				isActive: item.isActive ?? true,
				createdAt: item.createdAt || new Date(),
				createdBy: item.createdBy,
				updatedAt: item.updatedAt,
				updatedBy: item.updatedBy,
				role: item.role,
			})),
			pagination: {
				totalFound,
				page,
				limit,
				totalPages,
			},
		};
	}

	// mapToUser(user: any): User {
	// 	return {
	// 		id: user.id,
	// 		username: user.username,
	// 		name: user.name,
	// 		email: user.email,
	// 		roleId: user.roleId,
	// 		createdAt: user.createdAt,
	// 		createdBy: user.createdBy,
	// 		updatedAt: user.updatedAt,
	// 		updatedBy: user.updatedBy,
	// 	} as User;
	// }
}
