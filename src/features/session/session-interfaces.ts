export type SessionPayload = {
	user: {
		id: string;
		email?: string;
		name?: string;
		username?: string;
		roleId?: number;
		image?: string;
	};
	expires?: Date;
};

export interface ISessionService {
	// encrypt(payload: SessionPayload): Promise<string>;
	// decrypt(input: string): Promise<SessionPayload>;
	createSession(payload: SessionPayload): Promise<void>;
	updateSession(): Promise<void>;
	deleteSession(): Promise<void>;
	getSession(): Promise<SessionPayload | null>;
}
