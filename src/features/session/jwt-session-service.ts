import type { ISessionService, SessionPayload } from "./session-interfaces";
import { type JWTPayload, jwtVerify, SignJWT, CompactEncrypt, compactDecrypt } from "jose";
import { cookies } from "next/headers";

type CustomJWTPayload = JWTPayload & SessionPayload;

export class JwtSessionService implements ISessionService {
	private secretKey: string;
	private key;
	private encKey;

	constructor() {
		this.secretKey = process.env.JWT_SECRET ?? "secret-mantapjiwa";
		if (!process.env.JWT_SECRET) {
			console.warn("JWT_SECRET not set in environment variables. Using predefined secret.");
		}
		this.key = new TextEncoder().encode(this.secretKey);

		// For JWE encryption, create a fixed-length 32-byte key
		if (process.env.JWT_ENC_SECRET) {
			// If provided, use the environment variable but ensure it's 32 bytes
			const envKey = new TextEncoder().encode(process.env.JWT_ENC_SECRET);
			this.encKey = new Uint8Array(32);
			this.encKey.set(envKey.subarray(0, 32));
			// Pad with zeros if too short
			if (envKey.length < 32) {
				console.warn(`JWT_ENC_SECRET is too short (${envKey.length} bytes). Padding with zeros.`);
			}
		} else {
			// Generate a secure random 32-byte key
			// this.encKey = new Uint8Array(32);
			// const randomBytesArray = randomBytes(32);
			// for (let i = 0; i < 32; i++) {
			// 	this.encKey[i] = randomBytesArray[i];
			// }
			const tempFixedSecret = "0123456789abcdef0123456789abcdef";
			this.encKey = new TextEncoder().encode(tempFixedSecret);
			console.warn("JWT_ENC_SECRET not set. Using a randomly generated key.");
		}

		// Verify key length
		console.log(`Encryption key length: ${this.encKey.length * 8} bits`);
	}
	// Implement the methods
	async encrypt(payload: SessionPayload): Promise<string> {
		const customPayload: CustomJWTPayload = {
			user: payload.user,
			expires: payload.expires,
		};

		// First sign the JWT
		const jwt = await new SignJWT(customPayload)
			.setSubject(customPayload.user.id)
			.setProtectedHeader({ alg: "HS256" })
			.setIssuedAt()
			.setExpirationTime("24h")
			.sign(this.key);

		// console.log("Signed JWT:", `${jwt.substring(0, 20)}...`);

		// Then encrypt the JWT
		const encrypted = await new CompactEncrypt(new TextEncoder().encode(jwt))
			.setProtectedHeader({ alg: "dir", enc: "A256GCM" })
			.encrypt(this.encKey);

		// console.log("Encrypted JWT:", `${encrypted.substring(0, 20)}...`);
		return encrypted;
	}

	async decrypt(input: string): Promise<SessionPayload> {
		// First decrypt the JWE
		const { plaintext } = await compactDecrypt(input, this.encKey);
		const jwt = new TextDecoder().decode(plaintext);

		// Then verify the JWT
		const { payload } = await jwtVerify<CustomJWTPayload>(jwt, this.key, {
			algorithms: ["HS256"],
		});
		return payload;
	}

	async createSession(payload: SessionPayload): Promise<void> {
		if (!payload.expires) {
			payload.expires = new Date(Date.now() + 24 * 60 * 60 * 1000);
		}
		const session = await this.encrypt(payload);
		// Save the session in a cookie
		(await cookies()).set("session", session, { expires: payload.expires, httpOnly: true });
	}

	async updateSession(): Promise<void> {
		const session = (await cookies()).get("session")?.value;
		if (!session) return;
		const payload = await this.decrypt(session);
		if (!payload) return;

		const expires = new Date(Date.now() + 24 * 60 * 60 * 1000);

		const cookieStore = await cookies();
		cookieStore.set("session", session, {
			httpOnly: true,
			expires: expires,
			// secure: true,
			// sameSite: "lax",
			// path: "/",
		});
	}

	async deleteSession(): Promise<void> {
		// Destroy the session
		// (await cookies()).set("session", "", { expires: new Date(0) });
		const cookieStore = await cookies();
		cookieStore.delete("session");
	}

	async getSession(): Promise<SessionPayload | null> {
		const session = (await cookies()).get("session")?.value;
		if (!session) return null;
		const currentSession = await this.decrypt(session);
		return currentSession;
	}
}
