import { db } from "@/common/db/drizzle";
import type { AuthResult, IAuthService, IHasher, SignInParams } from "./auth-interfaces";
import { BcryptHasher } from "./bcrypt-hasher";

export class DrizzleAuthService implements IAuthService {
	private hasher: IHasher;

	constructor() {
		this.hasher = new BcryptHasher();
	}

	async signIn(params: SignInParams): Promise<AuthResult> {
		switch (params.provider) {
			case "credentials": {
				if (!params.credentials) {
					throw new Error("Invalid credentials");
				}
				const user = await db.query.usersTable.findFirst({
					where: (usersTable, { eq }) => eq(usersTable.username, params.credentials?.username || ""),
				});
				if (!user) {
					return {
						error: true,
						message: "User not found",
					};
				}

				const isPasswordValid = await this.hasher.verify(params.credentials?.password, user.password);
				if (!isPasswordValid) {
					return {
						error: true,
						message: "Invalid password",
					};
				}
				return {
					error: false,
					user: {
						id: user.id,
						username: user.username,
						name: user.name || "",
						email: user.email,
						roleId: user.roleId || 0,
					},
				};
			}
			case "google":
			case "github":
				// Placeholder for future implementation
				return {
					error: true,
					message: "Provider not implemented yet",
				};
			default:
				return {
					error: true,
					message: "Unknown provider",
				};
		}
	}

	async signOut(): Promise<void> {}
}
