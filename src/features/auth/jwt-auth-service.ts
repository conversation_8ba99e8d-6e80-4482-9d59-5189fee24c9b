import type { <PERSON>th<PERSON><PERSON><PERSON>, <PERSON><PERSON>uthSer<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Hash<PERSON>, SignInP<PERSON><PERSON>, User } from "./auth-interfaces";
import { db } from "@/common/db/drizzle";
import { BcryptHasher } from "./bcrypt-hasher";
import { type JWTPayload, jwtVerify, SignJWT, CompactEncrypt, compactDecrypt } from "jose";
import { cookies } from "next/headers";

type CustomJWTPayload = JWTPayload & {
	user: {
		id: string;
		email?: string;
		name?: string;
		username?: string;
		roleId?: number;
	};
	expires: Date;
};

export class JwtAuthService_Deprecated__ implements IAuthService {
	private hasher: IHasher;
	private secretKey: string;
	private key;
	private encKey;

	constructor() {
		this.hasher = new BcryptHasher();
		this.secretKey = process.env.JWT_SECRET ?? "secret-mantapjiwa";
		if (!process.env.JWT_SECRET) {
			console.warn("JWT_SECRET not set in environment variables. Using predefined secret.");
		}
		this.key = new TextEncoder().encode(this.secretKey);

		// For JWE encryption, create a fixed-length 32-byte key
		if (process.env.JWT_ENC_SECRET) {
			// If provided, use the environment variable but ensure it's 32 bytes
			const envKey = new TextEncoder().encode(process.env.JWT_ENC_SECRET);
			this.encKey = new Uint8Array(32);
			this.encKey.set(envKey.subarray(0, 32));
			// Pad with zeros if too short
			if (envKey.length < 32) {
				console.warn(`JWT_ENC_SECRET is too short (${envKey.length} bytes). Padding with zeros.`);
			}
		} else {
			// Generate a secure random 32-byte key
			// this.encKey = new Uint8Array(32);
			// const randomBytesArray = randomBytes(32);
			// for (let i = 0; i < 32; i++) {
			// 	this.encKey[i] = randomBytesArray[i];
			// }
			const tempFixedSecret = "0123456789abcdef0123456789abcdef";
			this.encKey = new TextEncoder().encode(tempFixedSecret);
			console.warn("JWT_ENC_SECRET not set. Using a randomly generated key.");
		}

		// Verify key length
		console.log(`Encryption key length: ${this.encKey.length * 8} bits`);
	}

	private async encrypt(payload: CustomJWTPayload) {
		// First sign the JWT
		const jwt = await new SignJWT(payload)
			.setSubject(payload.user.id)
			.setProtectedHeader({ alg: "HS256" })
			.setIssuedAt()
			.setExpirationTime("24h")
			.sign(this.key);

		// console.log("Signed JWT:", `${jwt.substring(0, 20)}...`);

		// Then encrypt the JWT
		const encrypted = await new CompactEncrypt(new TextEncoder().encode(jwt))
			.setProtectedHeader({ alg: "dir", enc: "A256GCM" })
			.encrypt(this.encKey);

		// console.log("Encrypted JWT:", `${encrypted.substring(0, 20)}...`);
		return encrypted;
	}

	private async decrypt(input: string) {
		// First decrypt the JWE
		const { plaintext } = await compactDecrypt(input, this.encKey);
		const jwt = new TextDecoder().decode(plaintext);

		// Then verify the JWT
		const { payload } = await jwtVerify<CustomJWTPayload>(jwt, this.key, {
			algorithms: ["HS256"],
		});
		return payload;
	}

	async getSession(): Promise<AuthSession | null> {
		const session = (await cookies()).get("session")?.value;
		if (!session) return null;
		const currentSession = await this.decrypt(session);
		return {
			user: {
				id: currentSession.user.id,
				email: currentSession.user.email,
				name: currentSession.user.name || "",
				username: currentSession.user.username || "",
				roleId: currentSession.user.roleId || 0,
			},
			expires: currentSession.expires,
		};
	}

	async signIn(params: SignInParams): Promise<AuthResult> {
		switch (params.provider) {
			case "credentials": {
				if (!params.credentials) {
					throw new Error("Invalid credentials");
				}
				const user = await db.query.usersTable.findFirst({
					where: (usersTable, { eq }) => eq(usersTable.username, params.credentials?.username || ""),
				});
				if (!user) {
					return {
						error: true,
						message: "User not found",
					};
				}

				const isPasswordValid = await this.hasher.verify(params.credentials?.password, user.password);
				if (!isPasswordValid) {
					return {
						error: true,
						message: "Invalid password",
					};
				}
				const expires = new Date(Date.now() + 24 * 60 * 60 * 1000);
				const session = await this.encrypt({
					user: {
						id: user.id,
						email: user.email,
						name: user.name ?? "",
						username: user.username,
						roleId: user.roleId || 0,
					},
					expires,
				});

				// Save the session in a cookie
				(await cookies()).set("session", session, { expires, httpOnly: true });
				return {
					error: false,
					user: {
						id: user.id,
						username: user.username,
						name: user.name || "",
						email: user.email,
						roleId: user.roleId || 0,
					},
				};
			}
			case "google":
			case "github":
				// Placeholder for future implementation
				return {
					error: true,
					message: "Provider not implemented yet",
				};
			default:
				return {
					error: true,
					message: "Unknown provider",
				};
		}
	}

	async signOut(): Promise<void> {
		// Destroy the session
		(await cookies()).set("session", "", { expires: new Date(0) });
	}

	async getUser(): Promise<User | null> {
		return null;
	}
}
