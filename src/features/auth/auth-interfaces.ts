export interface User {
	id: string;
	username?: string;
	name?: string;
	email?: string;
	image?: string;
	passwordHash?: string;
	roleId?: number;
}

export interface SignInParams {
	provider: string;
	credentials?: Credentials;
	redirectUrl?: string;
}

export interface Credentials {
	username: string;
	password: string;
}

export interface AuthSession {
	user: User | null;
	expires: Date;
}

export interface IHasher {
	hash(password: string): Promise<string>;
	verify(password: string, hash: string): Promise<boolean>;
	generateRandomPassword(): string;
}

export interface AuthResult {
	error?: boolean;
	user?: User;
	message?: string;
}

export interface IAuthService {
	// getSession(): Promise<AuthSession | null>;
	// updateSession(req: any): Promise<void>;
	signIn(params: SignInParams): Promise<AuthResult>;
	signOut(): Promise<void>;
	// signUp(params: RegisterParams): Promise<AuthResult>;
	// activate(params: { userId: string, activationCode: string}): Promise<boolean>;
	// getUser(token: string): Promise<User | null>;
}
