import "server-only";
import * as bcrypt from "bcrypt-ts";
import type { IHasher } from "./auth-interfaces";

const SALT_ROUNDS = 10;

export class BcryptHasher implements IHasher {
	async hash(password: string): Promise<string> {
		return bcrypt.hash(password, SALT_ROUNDS);
	}

	async verify(password: string, hash: string): Promise<boolean> {
		return bcrypt.compare(password, hash);
	}

	generateRandomPassword(): string {
		return Math.random().toString(36).slice(2, 10);
	}
}
