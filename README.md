first rclone bisync
rclone bisync --verbose ~/Documents/dev/hexagon/hexagon-nextjs ninjok-gdrive:rclone/dev/hexagon/hexagon-nextjs --exclude "node_modules/**" --exclude ".next/**" --resync

subsequent bisync remove --resync
rclone bisync --verbose ~/Documents/dev/hexagon/hexagon-nextjs ninjok-gdrive:rclone/dev/hexagon/hexagon-nextjs --exclude "node_modules/**" --exclude ".next/**"

or use
pnpm rclone-bisync

npx drizzle-kit generate
npx drizzle-kit migrate


pnpm dlx tsx --env-file=.env.local ./scripts/