{"name": "hexagon-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "__rclone-bisync": "rclone bisync --verbose ~/Documents/dev/hexagon/hexagon-nextjs ninjok-gdrive:rclone/dev/hexagon/hexagon-nextjs --exclude 'node_modules/**' --exclude '.next/**'", "rclone-sync": "rclone sync --verbose /Users/<USER>/Documents/dev/hexagon/hexagon-nextjs gdrive-dev:labs/hexagon/hexagon-nextjs --exclude 'node_modules/**' --exclude '.next/**'"}, "dependencies": {"@casl/ability": "^6.7.3", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "bcrypt-ts": "^7.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "jose": "^6.0.11", "lucide-react": "^0.513.0", "next": "15.3.3", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "pg": "^8.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.1", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}